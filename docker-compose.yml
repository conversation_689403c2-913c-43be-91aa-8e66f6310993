# ============================================================================
# BOROUGE ESG INTELLIGENCE PLATFORM - DOCKER COMPOSE
# ============================================================================
# Complete development and production environment setup

version: '3.8'

services:
  # ============================================================================
  # FRONTEND SERVICE (React Application)
  # ============================================================================
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: development
    container_name: borouge-esg-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:3001
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - borouge-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ============================================================================
  # BACKEND SERVICE (Node.js API)
  # ============================================================================
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: borouge-esg-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - redis
    networks:
      - borouge-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ============================================================================
  # REDIS CACHE SERVICE
  # ============================================================================
  redis:
    image: redis:7-alpine
    container_name: borouge-esg-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-borouge123}
    volumes:
      - redis_data:/data
    networks:
      - borouge-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # NGINX REVERSE PROXY (Production)
  # ============================================================================
  nginx:
    image: nginx:alpine
    container_name: borouge-esg-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    networks:
      - borouge-network
    restart: unless-stopped
    profiles:
      - production

  # ============================================================================
  # MONITORING SERVICES
  # ============================================================================
  
  # Prometheus for metrics collection
  prometheus:
    image: prom/prometheus:latest
    container_name: borouge-esg-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    networks:
      - borouge-network
    restart: unless-stopped
    profiles:
      - monitoring

  # Grafana for metrics visualization
  grafana:
    image: grafana/grafana:latest
    container_name: borouge-esg-grafana
    ports:
      - "3003:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - borouge-network
    restart: unless-stopped
    profiles:
      - monitoring

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  borouge-network:
    driver: bridge
    name: borouge-esg-network

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  redis_data:
    name: borouge-esg-redis-data
  prometheus_data:
    name: borouge-esg-prometheus-data
  grafana_data:
    name: borouge-esg-grafana-data

# ============================================================================
# DEVELOPMENT OVERRIDES
# ============================================================================
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
