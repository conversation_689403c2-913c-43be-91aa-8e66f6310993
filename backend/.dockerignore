# ============================================================================
# BOROUGE ESG INTELLIGENCE PLATFORM - DOCKER IGNORE
# ============================================================================

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Temporary folders
tmp/
temp/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentation
README.md
*.md
docs/

# Test files
test/
tests/
*.test.js
*.spec.js
__tests__/
.jest/

# Build artifacts
build/
dist/

# Backup files
*.backup
*.bak
*.tmp

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
*.cache

# AI Provider cache
.groq_cache/
.gemini_cache/
.openai_cache/

# ESG specific
esg_reports/
intelligence_cache/
analytics_data/

# Supabase local
.supabase/

# Deployment
.vercel/
.netlify/

# Performance monitoring
.clinic/
clinic.json

# Memory dumps
*.heapsnapshot

# Certificate files
*.crt
*.cert
*.ca-bundle
*.key
*.pem
*.p12
*.pfx

# Archive files
*.zip
*.rar
*.7z

# Local configuration
config.local.js
config.local.json

# Package manager alternatives
yarn.lock
pnpm-lock.yaml
.pnpm-debug.log*

# Development tools
.eslintrc*
.prettierrc*
jest.config.js
nodemon.json

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Monitoring
.sentry-native-metro
.flipper
