# ============================================================================
# BOROUGE ESG INTELLIGENCE PLATFORM - BACKEND DOCKERFILE
# ============================================================================
# Multi-stage build for production-ready Node.js backend

# ============================================================================
# STAGE 1: Base Image with Node.js
# ============================================================================
FROM node:18-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies for better security and performance
RUN apk add --no-cache \
    dumb-init \
    curl \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S borouge -u 1001

# ============================================================================
# STAGE 2: Dependencies Installation
# ============================================================================
FROM base AS deps

# Copy package files
COPY package*.json ./

# Install dependencies with npm ci for faster, reliable builds
RUN npm ci --only=production && npm cache clean --force

# ============================================================================
# STAGE 3: Development Stage
# ============================================================================
FROM base AS development

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# ============================================================================
# STAGE 4: Production Build
# ============================================================================
FROM base AS production

# Copy production dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Remove development files
RUN rm -rf \
    .env.example \
    .gitignore \
    README.md \
    Dockerfile \
    .dockerignore \
    tests/ \
    *.test.js \
    *.spec.js

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Production startup
CMD ["dumb-init", "node", "server.js"]

# ============================================================================
# METADATA
# ============================================================================
LABEL maintainer="Borouge ESG Intelligence Team"
LABEL version="1.0.0"
LABEL description="Borouge ESG Intelligence Platform Backend"
LABEL org.opencontainers.image.source="https://github.com/Mitty530/Borouge"
LABEL org.opencontainers.image.title="Borouge ESG Backend"
LABEL org.opencontainers.image.description="Production-ready backend for Borouge ESG Intelligence Platform"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Borouge"
